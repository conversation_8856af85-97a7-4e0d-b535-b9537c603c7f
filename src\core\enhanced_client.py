"""
Enhanced TCP Client with better error handling, retry logic, and recovery capabilities.
"""

import socket
import json
import os
import time
import threading
from pathlib import Path
from typing import Optional, Callable, Dict, Any, List
from src.utils.logger import get_logger
from src.utils.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>tionError, <PERSON><PERSON><PERSON>, error_handler
from src.utils.file_utils import FileUtils
from src.utils.network_utils import NetworkUtils


class EnhancedFileTransferClient:
    """
    Enhanced TCP client with improved error handling and recovery.
    """
    
    def __init__(self, chunk_size: int = 8192, max_retries: int = 3, retry_delay: float = 1.0):
        """
        Initialize the enhanced file transfer client.
        
        Args:
            chunk_size: Size of data chunks for transfer
            max_retries: Maximum number of retry attempts
            retry_delay: Delay between retry attempts in seconds
        """
        self.chunk_size = chunk_size
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.logger = get_logger()
        self.error_handler = <PERSON>rror<PERSON><PERSON><PERSON>()
        
        # Connection state
        self.socket = None
        self.connected = False
        self.connection_lock = threading.Lock()
        
        # Transfer state
        self.transfer_active = False
        self.transfer_cancelled = False
        self.current_transfer = None
        
        # Statistics
        self.stats = {
            'bytes_sent': 0,
            'files_sent': 0,
            'errors': 0,
            'retries': 0,
            'start_time': None,
            'end_time': None
        }
        
        # Callbacks for GUI integration
        self.on_connected: Optional[Callable] = None
        self.on_disconnected: Optional[Callable] = None
        self.on_transfer_progress: Optional[Callable] = None
        self.on_transfer_complete: Optional[Callable] = None
        self.on_transfer_error: Optional[Callable] = None
        self.on_retry_attempt: Optional[Callable] = None
    
    @error_handler("NET_001", reraise=False)
    def connect(self, host: str, port: int, timeout: float = 10.0) -> bool:
        """
        Connect to the file transfer server with validation and retry logic.
        
        Args:
            host: Server host address
            port: Server port number
            timeout: Connection timeout in seconds
            
        Returns:
            True if connected successfully, False otherwise
        """
        # Validate input
        if not Validator.validate_host(host):
            self.error_handler.handle_error("NET_003", context={"host": host})
            return False
        
        if not Validator.validate_port(port):
            self.error_handler.handle_error("NET_004", context={"port": port})
            return False
        
        with self.connection_lock:
            if self.connected:
                self.logger.warning("Already connected to server")
                return True
            
            # Resolve hostname if needed
            resolved_host = self._resolve_host(host)
            if not resolved_host:
                self.error_handler.handle_error("NET_003", context={"host": host, "resolved": False})
                return False
            
            # Attempt connection with retries
            for attempt in range(self.max_retries + 1):
                try:
                    self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    self.socket.settimeout(timeout)
                    self.socket.connect((resolved_host, port))
                    
                    self.connected = True
                    self.logger.info(f"Connected to server {resolved_host}:{port}")
                    
                    if self.on_connected:
                        self.on_connected(host, port)
                    
                    return True
                    
                except socket.timeout:
                    self.error_handler.handle_error("NET_002", context={"host": resolved_host, "port": port, "attempt": attempt + 1})
                except socket.error as e:
                    if e.errno == 10061:  # Connection refused
                        self.error_handler.handle_error("NET_001", e, {"host": resolved_host, "port": port, "attempt": attempt + 1})
                    else:
                        self.error_handler.handle_error("NET_001", e, {"host": resolved_host, "port": port, "attempt": attempt + 1})
                except Exception as e:
                    self.error_handler.handle_error("SYS_002", e, {"host": resolved_host, "port": port, "attempt": attempt + 1})
                
                # Clean up failed socket
                if self.socket:
                    try:
                        self.socket.close()
                    except:
                        pass
                    self.socket = None
                
                # Wait before retry (except on last attempt)
                if attempt < self.max_retries:
                    self.stats['retries'] += 1
                    if self.on_retry_attempt:
                        self.on_retry_attempt(attempt + 1, self.max_retries)
                    time.sleep(self.retry_delay * (attempt + 1))  # Exponential backoff
            
            return False
    
    def disconnect(self):
        """Disconnect from the server."""
        with self.connection_lock:
            self.connected = False
            self.transfer_cancelled = True
            
            if self.socket:
                try:
                    self.socket.close()
                except:
                    pass
                self.socket = None
            
            self.logger.info("Disconnected from server")
            
            if self.on_disconnected:
                self.on_disconnected()
    
    @error_handler("FS_001", reraise=False)
    def send_file(self, file_path: str, resume: bool = True) -> bool:
        """
        Send a file to the connected server with enhanced error handling.
        
        Args:
            file_path: Path to the file to send
            resume: Whether to attempt resume on failure
            
        Returns:
            True if file sent successfully, False otherwise
        """
        if not self.connected or not self.socket:
            self.error_handler.handle_error("NET_001", context={"operation": "send_file"})
            return False
        
        # Validate file
        if not Validator.validate_file_path(file_path):
            self.error_handler.handle_error("FS_001", context={"file_path": file_path})
            return False
        
        file_path = Path(file_path)
        
        # Check file accessibility
        if not FileUtils.is_file_accessible(str(file_path)):
            self.error_handler.handle_error("FS_002", context={"file_path": str(file_path)})
            return False
        
        # Check file size
        file_size = FileUtils.get_file_size(str(file_path))
        if file_size == 0:
            self.error_handler.handle_error("FS_001", context={"file_path": str(file_path), "size": 0})
            return False
        
        # Start transfer
        self.transfer_active = True
        self.transfer_cancelled = False
        self.current_transfer = {
            'file_path': str(file_path),
            'file_size': file_size,
            'bytes_sent': 0,
            'start_time': time.time()
        }
        
        try:
            # Prepare file metadata
            metadata = {
                "filename": file_path.name,
                "size": file_size,
                "checksum": FileUtils.calculate_md5(str(file_path))
            }
            
            self.logger.info(f"Sending file: {file_path.name} ({FileUtils.format_file_size(file_size)})")
            
            # Send metadata
            if not self._send_metadata(metadata):
                return False
            
            # Send file content with progress tracking
            if not self._send_file_content_enhanced(file_path, file_size):
                return False
            
            # Receive response from server
            response = self._receive_response()
            if response and response.get("status") == "success":
                self.logger.info(f"File sent successfully: {file_path.name}")
                self.stats['files_sent'] += 1
                self.stats['bytes_sent'] += file_size
                
                if self.on_transfer_complete:
                    self.on_transfer_complete(file_path.name, True)
                
                return True
            else:
                self.error_handler.handle_error("NET_001", context={"file": file_path.name, "response": response})
                
                if self.on_transfer_error:
                    self.on_transfer_error(file_path.name, "Server reported error")
                
                return False
                
        except Exception as e:
            self.stats['errors'] += 1
            self.error_handler.handle_error("SYS_002", e, {"file": file_path.name})
            
            if self.on_transfer_error:
                self.on_transfer_error(file_path.name, str(e))
            
            return False
        finally:
            self.transfer_active = False
            self.current_transfer = None
    
    def send_multiple_files(self, file_paths: List[str], stop_on_error: bool = False) -> Dict[str, bool]:
        """
        Send multiple files with individual error handling.
        
        Args:
            file_paths: List of file paths to send
            stop_on_error: Whether to stop on first error
            
        Returns:
            Dictionary mapping file paths to success status
        """
        results = {}
        self.stats['start_time'] = time.time()
        
        try:
            for file_path in file_paths:
                if self.transfer_cancelled:
                    break
                
                success = self.send_file(file_path)
                results[file_path] = success
                
                if not success and stop_on_error:
                    break
            
            return results
            
        finally:
            self.stats['end_time'] = time.time()
    
    def cancel_transfer(self):
        """Cancel the current transfer."""
        self.transfer_cancelled = True
        self.logger.info("Transfer cancelled by user")
    
    def get_transfer_stats(self) -> Dict[str, Any]:
        """Get transfer statistics."""
        stats = self.stats.copy()
        
        if self.current_transfer:
            stats['current_file'] = self.current_transfer['file_path']
            stats['current_progress'] = (self.current_transfer['bytes_sent'] / self.current_transfer['file_size']) * 100
        
        if stats['start_time'] and stats['end_time']:
            duration = stats['end_time'] - stats['start_time']
            stats['duration'] = duration
            stats['average_speed'] = stats['bytes_sent'] / duration if duration > 0 else 0
        
        return stats
    
    def _resolve_host(self, host: str) -> Optional[str]:
        """Resolve hostname to IP address."""
        return NetworkUtils.resolve_hostname(host) or (host if NetworkUtils.is_valid_ip(host) else None)
    
    def _send_metadata(self, metadata: Dict[str, Any]) -> bool:
        """Send file metadata to server."""
        try:
            metadata_data = json.dumps(metadata).encode('utf-8')
            metadata_length = len(metadata_data)
            
            # Send metadata length (4 bytes) then metadata
            self.socket.send(metadata_length.to_bytes(4, byteorder='big'))
            self.socket.send(metadata_data)
            
            return True
            
        except Exception as e:
            self.error_handler.handle_error("NET_001", e, {"operation": "send_metadata"})
            return False
    
    def _send_file_content_enhanced(self, file_path: Path, file_size: int) -> bool:
        """Send file content with enhanced progress tracking and error handling."""
        try:
            sent_bytes = 0
            last_progress_update = 0
            
            with open(file_path, 'rb') as f:
                while sent_bytes < file_size and not self.transfer_cancelled:
                    # Read chunk
                    remaining = file_size - sent_bytes
                    chunk_size = min(self.chunk_size, remaining)
                    chunk = f.read(chunk_size)
                    
                    if not chunk:
                        break
                    
                    # Send chunk with retry logic
                    if not self._send_chunk_with_retry(chunk):
                        return False
                    
                    sent_bytes += len(chunk)
                    
                    # Update transfer state
                    if self.current_transfer:
                        self.current_transfer['bytes_sent'] = sent_bytes
                    
                    # Report progress (throttled to avoid UI spam)
                    current_time = time.time()
                    if current_time - last_progress_update >= 0.1:  # Update every 100ms
                        if self.on_transfer_progress:
                            progress = (sent_bytes / file_size) * 100
                            self.on_transfer_progress(file_path.name, progress, sent_bytes, file_size)
                        last_progress_update = current_time
            
            return sent_bytes == file_size and not self.transfer_cancelled
            
        except Exception as e:
            self.error_handler.handle_error("FS_002", e, {"file": str(file_path)})
            return False
    
    def _send_chunk_with_retry(self, chunk: bytes) -> bool:
        """Send a chunk with retry logic."""
        for attempt in range(self.max_retries + 1):
            try:
                self.socket.send(chunk)
                return True
            except socket.error as e:
                if attempt < self.max_retries:
                    self.logger.warning(f"Chunk send failed, retrying... (attempt {attempt + 1})")
                    time.sleep(0.1)  # Brief delay before retry
                else:
                    self.error_handler.handle_error("NET_001", e, {"operation": "send_chunk"})
                    return False
        return False
    
    def _receive_response(self) -> Optional[Dict[str, Any]]:
        """Receive response from server with timeout handling."""
        try:
            # Set a reasonable timeout for response
            original_timeout = self.socket.gettimeout()
            self.socket.settimeout(30.0)
            
            # Receive response length (4 bytes)
            length_data = self.socket.recv(4)
            if len(length_data) != 4:
                return None
            
            response_length = int.from_bytes(length_data, byteorder='big')
            
            # Receive response data
            response_data = b""
            while len(response_data) < response_length:
                chunk = self.socket.recv(response_length - len(response_data))
                if not chunk:
                    return None
                response_data += chunk
            
            response = json.loads(response_data.decode('utf-8'))
            
            # Restore original timeout
            self.socket.settimeout(original_timeout)
            
            return response
            
        except Exception as e:
            self.error_handler.handle_error("NET_002", e, {"operation": "receive_response"})
            return None
    
    def is_connected(self) -> bool:
        """Check if client is connected to server."""
        return self.connected and self.socket is not None
    
    def is_transfer_active(self) -> bool:
        """Check if a transfer is currently active."""
        return self.transfer_active

    def get_connection_info(self) -> Optional[Dict[str, Any]]:
        """Get current connection information."""
        if not self.connected or not self.socket:
            return None

        try:
            return NetworkUtils.get_connection_info(self.socket)
        except Exception:
            return None
