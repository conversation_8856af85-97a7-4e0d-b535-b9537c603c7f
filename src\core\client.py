"""
TCP Client implementation for file transfer.
"""

import socket
import json
import os
from pathlib import Path
from typing import Optional, Callable, Dict, Any
from src.utils.logger import get_logger


class FileTransferClient:
    """
    TCP client for sending files to server.
    """
    
    def __init__(self, chunk_size: int = 8192):
        """
        Initialize the file transfer client.
        
        Args:
            chunk_size: Size of data chunks for transfer
        """
        self.chunk_size = chunk_size
        self.logger = get_logger()
        self.socket = None
        self.connected = False
        
        # Callbacks for GUI integration
        self.on_connected: Optional[Callable] = None
        self.on_disconnected: Optional[Callable] = None
        self.on_transfer_progress: Optional[Callable] = None
        self.on_transfer_complete: Optional[Callable] = None
        self.on_transfer_error: Optional[Callable] = None
    
    def connect(self, host: str, port: int) -> bool:
        """
        Connect to the file transfer server.
        
        Args:
            host: Server host address
            port: Server port number
            
        Returns:
            True if connected successfully, False otherwise
        """
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(10)  # 10 second timeout for connection
            self.socket.connect((host, port))
            
            self.connected = True
            self.logger.info(f"Connected to server {host}:{port}")
            
            if self.on_connected:
                self.on_connected(host, port)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to connect to {host}:{port}: {e}")
            self.connected = False
            return False
    
    def disconnect(self):
        """Disconnect from the server."""
        self.connected = False
        
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
        
        self.logger.info("Disconnected from server")
        
        if self.on_disconnected:
            self.on_disconnected()
    
    def send_file(self, file_path: str) -> bool:
        """
        Send a file to the connected server.
        
        Args:
            file_path: Path to the file to send
            
        Returns:
            True if file sent successfully, False otherwise
        """
        if not self.connected or not self.socket:
            self.logger.error("Not connected to server")
            return False
        
        file_path = Path(file_path)
        
        if not file_path.exists():
            self.logger.error(f"File does not exist: {file_path}")
            return False
        
        if not file_path.is_file():
            self.logger.error(f"Path is not a file: {file_path}")
            return False
        
        try:
            # Prepare file metadata
            file_size = file_path.stat().st_size
            metadata = {
                "filename": file_path.name,
                "size": file_size,
                "checksum": self._calculate_checksum(file_path)
            }
            
            self.logger.info(f"Sending file: {file_path.name} ({file_size} bytes)")
            
            # Send metadata
            if not self._send_metadata(metadata):
                return False
            
            # Send file content
            if not self._send_file_content(file_path, file_size):
                return False
            
            # Receive response from server
            response = self._receive_response()
            if response and response.get("status") == "success":
                self.logger.info(f"File sent successfully: {file_path.name}")
                
                if self.on_transfer_complete:
                    self.on_transfer_complete(file_path.name, True)
                
                return True
            else:
                self.logger.error(f"Server reported error for file: {file_path.name}")
                
                if self.on_transfer_error:
                    self.on_transfer_error(file_path.name, "Server reported error")
                
                return False
                
        except Exception as e:
            self.logger.error(f"Error sending file {file_path.name}: {e}")
            
            if self.on_transfer_error:
                self.on_transfer_error(file_path.name, str(e))
            
            return False
    
    def _send_metadata(self, metadata: Dict[str, Any]) -> bool:
        """
        Send file metadata to server.
        
        Args:
            metadata: File metadata dictionary
            
        Returns:
            True if sent successfully, False otherwise
        """
        try:
            metadata_data = json.dumps(metadata).encode('utf-8')
            metadata_length = len(metadata_data)
            
            # Send metadata length (4 bytes) then metadata
            self.socket.send(metadata_length.to_bytes(4, byteorder='big'))
            self.socket.send(metadata_data)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending metadata: {e}")
            return False
    
    def _send_file_content(self, file_path: Path, file_size: int) -> bool:
        """
        Send file content to server.
        
        Args:
            file_path: Path to the file
            file_size: Size of the file
            
        Returns:
            True if sent successfully, False otherwise
        """
        try:
            sent_bytes = 0
            
            with open(file_path, 'rb') as f:
                while sent_bytes < file_size:
                    # Read chunk
                    chunk = f.read(self.chunk_size)
                    if not chunk:
                        break
                    
                    # Send chunk
                    self.socket.send(chunk)
                    sent_bytes += len(chunk)
                    
                    # Report progress
                    if self.on_transfer_progress:
                        progress = (sent_bytes / file_size) * 100
                        self.on_transfer_progress(file_path.name, progress, sent_bytes, file_size)
            
            return sent_bytes == file_size
            
        except Exception as e:
            self.logger.error(f"Error sending file content: {e}")
            return False
    
    def _receive_response(self) -> Optional[Dict[str, Any]]:
        """
        Receive response from server.
        
        Returns:
            Response dictionary or None if failed
        """
        try:
            # Receive response length (4 bytes)
            length_data = self.socket.recv(4)
            if len(length_data) != 4:
                return None
            
            response_length = int.from_bytes(length_data, byteorder='big')
            
            # Receive response data
            response_data = b""
            while len(response_data) < response_length:
                chunk = self.socket.recv(response_length - len(response_data))
                if not chunk:
                    return None
                response_data += chunk
            
            response = json.loads(response_data.decode('utf-8'))
            return response
            
        except Exception as e:
            self.logger.error(f"Error receiving response: {e}")
            return None
    
    def _calculate_checksum(self, file_path: Path) -> str:
        """
        Calculate file checksum for integrity verification.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Hexadecimal checksum string
        """
        import hashlib
        
        hash_md5 = hashlib.md5()
        
        try:
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            
            return hash_md5.hexdigest()
            
        except Exception as e:
            self.logger.error(f"Error calculating checksum: {e}")
            return ""
    
    def is_connected(self) -> bool:
        """
        Check if client is connected to server.
        
        Returns:
            True if connected, False otherwise
        """
        return self.connected and self.socket is not None
