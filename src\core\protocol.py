"""
File Transfer Protocol definitions and utilities.
"""

import json
import struct
from enum import Enum
from typing import Dict, Any, Optional
from dataclasses import dataclass


class MessageType(Enum):
    """Message types for the file transfer protocol."""
    FILE_METADATA = "file_metadata"
    FILE_CHUNK = "file_chunk"
    TRANSFER_COMPLETE = "transfer_complete"
    TRANSFER_ERROR = "transfer_error"
    HEARTBEAT = "heartbeat"
    DISCONNECT = "disconnect"


class TransferStatus(Enum):
    """Transfer status codes."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class FileMetadata:
    """File metadata structure."""
    filename: str
    size: int
    checksum: str
    chunk_size: int = 8192
    total_chunks: int = 0
    
    def __post_init__(self):
        """Calculate total chunks after initialization."""
        if self.size > 0 and self.chunk_size > 0:
            self.total_chunks = (self.size + self.chunk_size - 1) // self.chunk_size
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "filename": self.filename,
            "size": self.size,
            "checksum": self.checksum,
            "chunk_size": self.chunk_size,
            "total_chunks": self.total_chunks
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FileMetadata':
        """Create from dictionary."""
        return cls(
            filename=data["filename"],
            size=data["size"],
            checksum=data["checksum"],
            chunk_size=data.get("chunk_size", 8192),
            total_chunks=data.get("total_chunks", 0)
        )


@dataclass
class TransferProgress:
    """Transfer progress information."""
    filename: str
    bytes_transferred: int
    total_bytes: int
    chunks_transferred: int
    total_chunks: int
    speed_bps: float = 0.0
    eta_seconds: float = 0.0
    
    @property
    def progress_percentage(self) -> float:
        """Calculate progress percentage."""
        if self.total_bytes == 0:
            return 0.0
        return (self.bytes_transferred / self.total_bytes) * 100.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "filename": self.filename,
            "bytes_transferred": self.bytes_transferred,
            "total_bytes": self.total_bytes,
            "chunks_transferred": self.chunks_transferred,
            "total_chunks": self.total_chunks,
            "progress_percentage": self.progress_percentage,
            "speed_bps": self.speed_bps,
            "eta_seconds": self.eta_seconds
        }


class FileTransferProtocol:
    """
    Protocol handler for file transfer communication.
    """
    
    PROTOCOL_VERSION = "1.0"
    HEADER_SIZE = 8  # 4 bytes for message type + 4 bytes for payload length
    
    @staticmethod
    def create_message(message_type: MessageType, payload: Dict[str, Any]) -> bytes:
        """
        Create a protocol message.
        
        Args:
            message_type: Type of the message
            payload: Message payload
            
        Returns:
            Encoded message bytes
        """
        # Create message structure
        message = {
            "version": FileTransferProtocol.PROTOCOL_VERSION,
            "type": message_type.value,
            "payload": payload
        }
        
        # Encode to JSON bytes
        message_data = json.dumps(message).encode('utf-8')
        message_length = len(message_data)
        
        # Create header: message_type_id (4 bytes) + length (4 bytes)
        type_id = list(MessageType).index(message_type)
        header = struct.pack('>II', type_id, message_length)
        
        return header + message_data
    
    @staticmethod
    def parse_message(data: bytes) -> Optional[Dict[str, Any]]:
        """
        Parse a protocol message.
        
        Args:
            data: Raw message bytes
            
        Returns:
            Parsed message dictionary or None if invalid
        """
        try:
            if len(data) < FileTransferProtocol.HEADER_SIZE:
                return None
            
            # Parse header
            type_id, message_length = struct.unpack('>II', data[:FileTransferProtocol.HEADER_SIZE])
            
            # Validate message type
            if type_id >= len(MessageType):
                return None
            
            message_type = list(MessageType)[type_id]
            
            # Parse message data
            if len(data) < FileTransferProtocol.HEADER_SIZE + message_length:
                return None
            
            message_data = data[FileTransferProtocol.HEADER_SIZE:FileTransferProtocol.HEADER_SIZE + message_length]
            message = json.loads(message_data.decode('utf-8'))
            
            # Validate message structure
            if not all(key in message for key in ["version", "type", "payload"]):
                return None
            
            if message["type"] != message_type.value:
                return None
            
            return message
            
        except Exception:
            return None
    
    @staticmethod
    def create_file_metadata_message(metadata: FileMetadata) -> bytes:
        """
        Create a file metadata message.
        
        Args:
            metadata: File metadata
            
        Returns:
            Encoded message bytes
        """
        return FileTransferProtocol.create_message(
            MessageType.FILE_METADATA,
            metadata.to_dict()
        )
    
    @staticmethod
    def create_transfer_progress_message(progress: TransferProgress) -> bytes:
        """
        Create a transfer progress message.
        
        Args:
            progress: Transfer progress
            
        Returns:
            Encoded message bytes
        """
        return FileTransferProtocol.create_message(
            MessageType.FILE_CHUNK,
            progress.to_dict()
        )
    
    @staticmethod
    def create_error_message(error_code: str, error_message: str) -> bytes:
        """
        Create an error message.
        
        Args:
            error_code: Error code
            error_message: Error description
            
        Returns:
            Encoded message bytes
        """
        payload = {
            "error_code": error_code,
            "error_message": error_message
        }
        
        return FileTransferProtocol.create_message(
            MessageType.TRANSFER_ERROR,
            payload
        )
    
    @staticmethod
    def create_success_message(message: str = "Transfer completed successfully") -> bytes:
        """
        Create a success message.
        
        Args:
            message: Success message
            
        Returns:
            Encoded message bytes
        """
        payload = {"message": message}
        
        return FileTransferProtocol.create_message(
            MessageType.TRANSFER_COMPLETE,
            payload
        )
    
    @staticmethod
    def create_heartbeat_message() -> bytes:
        """
        Create a heartbeat message.
        
        Returns:
            Encoded message bytes
        """
        payload = {"timestamp": str(int(time.time()))}
        
        return FileTransferProtocol.create_message(
            MessageType.HEARTBEAT,
            payload
        )


# Import time for heartbeat
import time
