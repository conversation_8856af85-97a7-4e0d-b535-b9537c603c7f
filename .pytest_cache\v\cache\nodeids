["tests/test_core.py::TestEncryption::test_aes_encryption", "tests/test_core.py::TestEncryption::test_rsa_key_exchange", "tests/test_core.py::TestFileTransfer::test_file_transfer", "tests/test_core.py::TestFileTransfer::test_server_client_connection", "tests/test_core.py::TestFileTransferProtocol::test_create_and_parse_message", "tests/test_core.py::TestFileTransferProtocol::test_file_metadata", "tests/test_core.py::TestFileUtils::test_calculate_md5", "tests/test_core.py::TestFileUtils::test_format_file_size", "tests/test_core.py::TestFileUtils::test_is_safe_filename", "tests/test_core.py::TestFileUtils::test_sanitize_filename", "tests/test_core.py::TestNetworkUtils::test_find_available_port", "tests/test_core.py::TestNetworkUtils::test_is_valid_ip", "tests/test_core.py::TestNetworkUtils::test_is_valid_port"]