"""
Settings dialog for the file transfer application.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from pathlib import Path
from typing import Dict, Any, Optional
from src.utils.network_utils import NetworkUtils


class SettingsDialog:
    """
    Dialog for configuring application settings.
    """
    
    def __init__(self, parent: tk.Tk, current_settings: Optional[Dict[str, Any]] = None):
        """
        Initialize the settings dialog.
        
        Args:
            parent: Parent window
            current_settings: Current application settings
        """
        self.parent = parent
        self.settings = current_settings or self._get_default_settings()
        self.result = None
        
        # Create dialog
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Settings")
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        
        # Make dialog modal
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center dialog
        self._center_dialog()
        
        # Create widgets
        self._create_widgets()
        
        # Handle dialog close
        self.dialog.protocol("WM_DELETE_WINDOW", self._on_cancel)
    
    def _get_default_settings(self) -> Dict[str, Any]:
        """Get default application settings."""
        return {
            "chunk_size_kb": 8,
            "connection_timeout": 10,
            "max_connections": 5,
            "enable_encryption": False,
            "verify_checksums": True,
            "default_download_dir": str(Path.home() / "Downloads" / "FileTransfer"),
            "auto_start_server": False,
            "default_server_port": 8888,
            "default_server_host": "0.0.0.0"
        }
    
    def _create_widgets(self):
        """Create and layout dialog widgets."""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create notebook for settings categories
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # Network settings tab
        self._create_network_tab(notebook)
        
        # Security settings tab
        self._create_security_tab(notebook)
        
        # General settings tab
        self._create_general_tab(notebook)
        
        # Buttons frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        # Buttons
        ttk.Button(button_frame, text="OK", command=self._on_ok).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="Cancel", command=self._on_cancel).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="Apply", command=self._on_apply).pack(side=tk.RIGHT, padx=(0, 5))
        ttk.Button(button_frame, text="Reset to Defaults", command=self._on_reset).pack(side=tk.LEFT)
    
    def _create_network_tab(self, notebook: ttk.Notebook):
        """Create the network settings tab."""
        network_frame = ttk.Frame(notebook, padding="10")
        notebook.add(network_frame, text="Network")
        
        # Chunk size
        ttk.Label(network_frame, text="Chunk Size (KB):").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.chunk_size_var = tk.StringVar(value=str(self.settings["chunk_size_kb"]))
        chunk_entry = ttk.Entry(network_frame, textvariable=self.chunk_size_var, width=10)
        chunk_entry.grid(row=0, column=1, sticky=tk.W, pady=(0, 5))
        ttk.Label(network_frame, text="(1-1024, recommended: 8-64)").grid(row=0, column=2, sticky=tk.W, padx=(10, 0), pady=(0, 5))
        
        # Connection timeout
        ttk.Label(network_frame, text="Connection Timeout (seconds):").grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
        self.timeout_var = tk.StringVar(value=str(self.settings["connection_timeout"]))
        timeout_entry = ttk.Entry(network_frame, textvariable=self.timeout_var, width=10)
        timeout_entry.grid(row=1, column=1, sticky=tk.W, pady=(0, 5))
        
        # Max connections
        ttk.Label(network_frame, text="Max Concurrent Connections:").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        self.max_conn_var = tk.StringVar(value=str(self.settings["max_connections"]))
        max_conn_entry = ttk.Entry(network_frame, textvariable=self.max_conn_var, width=10)
        max_conn_entry.grid(row=2, column=1, sticky=tk.W, pady=(0, 5))
        
        # Default server settings
        ttk.Label(network_frame, text="Default Server Host:").grid(row=3, column=0, sticky=tk.W, pady=(10, 5))
        self.server_host_var = tk.StringVar(value=self.settings["default_server_host"])
        host_combo = ttk.Combobox(network_frame, textvariable=self.server_host_var, width=20)
        host_combo['values'] = ['0.0.0.0', 'localhost'] + NetworkUtils.get_all_local_ips()
        host_combo.grid(row=3, column=1, columnspan=2, sticky=tk.W, pady=(10, 5))
        
        ttk.Label(network_frame, text="Default Server Port:").grid(row=4, column=0, sticky=tk.W, pady=(0, 5))
        self.server_port_var = tk.StringVar(value=str(self.settings["default_server_port"]))
        port_entry = ttk.Entry(network_frame, textvariable=self.server_port_var, width=10)
        port_entry.grid(row=4, column=1, sticky=tk.W, pady=(0, 5))
    
    def _create_security_tab(self, notebook: ttk.Notebook):
        """Create the security settings tab."""
        security_frame = ttk.Frame(notebook, padding="10")
        notebook.add(security_frame, text="Security")
        
        # Enable encryption
        self.encryption_var = tk.BooleanVar(value=self.settings["enable_encryption"])
        encryption_check = ttk.Checkbutton(
            security_frame,
            text="Enable file encryption",
            variable=self.encryption_var
        )
        encryption_check.grid(row=0, column=0, sticky=tk.W, pady=(0, 10))
        
        # Encryption info
        encryption_info = ttk.Label(
            security_frame,
            text="When enabled, files are encrypted using AES-256 before transfer.\nThis provides security but may reduce transfer speed.",
            justify=tk.LEFT,
            foreground="gray"
        )
        encryption_info.grid(row=1, column=0, sticky=tk.W, pady=(0, 20))
        
        # Verify checksums
        self.checksum_var = tk.BooleanVar(value=self.settings["verify_checksums"])
        checksum_check = ttk.Checkbutton(
            security_frame,
            text="Verify file checksums",
            variable=self.checksum_var
        )
        checksum_check.grid(row=2, column=0, sticky=tk.W, pady=(0, 10))
        
        # Checksum info
        checksum_info = ttk.Label(
            security_frame,
            text="When enabled, file integrity is verified using MD5 checksums.\nThis ensures files are transferred without corruption.",
            justify=tk.LEFT,
            foreground="gray"
        )
        checksum_info.grid(row=3, column=0, sticky=tk.W)
    
    def _create_general_tab(self, notebook: ttk.Notebook):
        """Create the general settings tab."""
        general_frame = ttk.Frame(notebook, padding="10")
        notebook.add(general_frame, text="General")
        
        # Default download directory
        ttk.Label(general_frame, text="Default Download Directory:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        dir_frame = ttk.Frame(general_frame)
        dir_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        dir_frame.columnconfigure(0, weight=1)
        
        self.download_dir_var = tk.StringVar(value=self.settings["default_download_dir"])
        dir_entry = ttk.Entry(dir_frame, textvariable=self.download_dir_var)
        dir_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        
        ttk.Button(dir_frame, text="Browse", command=self._browse_download_dir).grid(row=0, column=1)
        
        # Auto-start server
        self.auto_start_var = tk.BooleanVar(value=self.settings["auto_start_server"])
        auto_start_check = ttk.Checkbutton(
            general_frame,
            text="Auto-start server on application launch",
            variable=self.auto_start_var
        )
        auto_start_check.grid(row=2, column=0, sticky=tk.W, pady=(10, 0))
        
        # Configure grid weights
        general_frame.columnconfigure(0, weight=1)
    
    def _center_dialog(self):
        """Center the dialog on the parent window."""
        self.dialog.update_idletasks()
        
        # Get parent window position and size
        parent_x = self.parent.winfo_x()
        parent_y = self.parent.winfo_y()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        # Get dialog size
        dialog_width = self.dialog.winfo_width()
        dialog_height = self.dialog.winfo_height()
        
        # Calculate center position
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
        
        self.dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
    
    def _browse_download_dir(self):
        """Browse for download directory."""
        directory = filedialog.askdirectory(
            title="Select Default Download Directory",
            initialdir=self.download_dir_var.get()
        )
        
        if directory:
            self.download_dir_var.set(directory)
    
    def _validate_settings(self) -> bool:
        """Validate the current settings."""
        try:
            # Validate chunk size
            chunk_size = int(self.chunk_size_var.get())
            if not (1 <= chunk_size <= 1024):
                messagebox.showerror("Invalid Setting", "Chunk size must be between 1 and 1024 KB")
                return False
            
            # Validate timeout
            timeout = int(self.timeout_var.get())
            if not (1 <= timeout <= 300):
                messagebox.showerror("Invalid Setting", "Connection timeout must be between 1 and 300 seconds")
                return False
            
            # Validate max connections
            max_conn = int(self.max_conn_var.get())
            if not (1 <= max_conn <= 100):
                messagebox.showerror("Invalid Setting", "Max connections must be between 1 and 100")
                return False
            
            # Validate server port
            server_port = int(self.server_port_var.get())
            if not NetworkUtils.is_valid_port(server_port):
                messagebox.showerror("Invalid Setting", "Server port must be between 1 and 65535")
                return False
            
            # Validate download directory
            download_dir = self.download_dir_var.get().strip()
            if not download_dir:
                messagebox.showerror("Invalid Setting", "Download directory cannot be empty")
                return False
            
            return True
            
        except ValueError:
            messagebox.showerror("Invalid Setting", "Please enter valid numeric values")
            return False
    
    def _get_current_settings(self) -> Dict[str, Any]:
        """Get the current settings from the dialog."""
        return {
            "chunk_size_kb": int(self.chunk_size_var.get()),
            "connection_timeout": int(self.timeout_var.get()),
            "max_connections": int(self.max_conn_var.get()),
            "enable_encryption": self.encryption_var.get(),
            "verify_checksums": self.checksum_var.get(),
            "default_download_dir": self.download_dir_var.get().strip(),
            "auto_start_server": self.auto_start_var.get(),
            "default_server_port": int(self.server_port_var.get()),
            "default_server_host": self.server_host_var.get()
        }
    
    def _on_ok(self):
        """Handle OK button click."""
        if self._validate_settings():
            self.result = self._get_current_settings()
            self.dialog.destroy()
    
    def _on_cancel(self):
        """Handle Cancel button click."""
        self.result = None
        self.dialog.destroy()
    
    def _on_apply(self):
        """Handle Apply button click."""
        if self._validate_settings():
            self.result = self._get_current_settings()
            # Don't close dialog, just return the settings
    
    def _on_reset(self):
        """Reset settings to defaults."""
        defaults = self._get_default_settings()
        
        self.chunk_size_var.set(str(defaults["chunk_size_kb"]))
        self.timeout_var.set(str(defaults["connection_timeout"]))
        self.max_conn_var.set(str(defaults["max_connections"]))
        self.encryption_var.set(defaults["enable_encryption"])
        self.checksum_var.set(defaults["verify_checksums"])
        self.download_dir_var.set(defaults["default_download_dir"])
        self.auto_start_var.set(defaults["auto_start_server"])
        self.server_port_var.set(str(defaults["default_server_port"]))
        self.server_host_var.set(defaults["default_server_host"])
    
    def show(self) -> Optional[Dict[str, Any]]:
        """
        Show the dialog and return the settings.
        
        Returns:
            Settings dictionary or None if cancelled
        """
        self.dialog.wait_window()
        return self.result
