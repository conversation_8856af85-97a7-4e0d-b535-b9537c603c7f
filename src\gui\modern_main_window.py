"""
Modern main GUI window with professional design and enhanced functionality.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import time
from pathlib import Path
from typing import Optional, List, Dict, Any
from src.core.server import FileTransferServer
from src.core.client import FileTransferClient
from src.core.enhanced_client import EnhancedFileTransferClient
from src.utils.network_utils import NetworkUtils
from src.utils.file_utils import FileUtils
from src.utils.logger import get_logger
from src.gui.theme import ModernTheme, IconManager
from src.gui.connection_manager import ConnectionManager, ConnectionDialog
from src.gui.progress_dialog import ProgressDialog


class ModernMainWindow:
    """
    Modern main application window with professional design.
    """
    
    def __init__(self):
        """Initialize the modern main window."""
        self.logger = get_logger()
        
        # Create main window
        self.root = tk.Tk()
        self.root.title("File Transfer Pro")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # Apply modern theme
        self.style = ModernTheme.apply_theme(self.root)
        
        # Application state
        self.server: Optional[FileTransferServer] = None
        self.client: Optional[FileTransferClient] = None
        self.server_thread: Optional[threading.Thread] = None
        self.connection_manager = ConnectionManager()
        
        # UI state
        self.selected_files: List[str] = []
        self.transfer_in_progress = False
        
        # Setup GUI
        self._create_widgets()
        
        # Center window
        self._center_window()
        
        # Handle window close
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
        
        # Initialize status
        self._update_status("Ready")
    
    def _create_widgets(self):
        """Create and layout GUI widgets."""
        # Main container with modern styling
        main_frame = ttk.Frame(self.root, padding=ModernTheme.SPACING['lg'])
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Create header
        self._create_header(main_frame)
        
        # Create main content area
        self._create_main_content(main_frame)
        
        # Create status bar
        self._create_status_bar(main_frame)
    
    def _create_header(self, parent):
        """Create the modern header."""
        header_frame = ttk.Frame(parent, style='Card.TFrame', padding=ModernTheme.SPACING['lg'])
        header_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, ModernTheme.SPACING['lg']))
        header_frame.columnconfigure(1, weight=1)
        
        # App title and icon
        title_frame = ttk.Frame(header_frame)
        title_frame.grid(row=0, column=0, sticky=tk.W)
        
        app_icon = ttk.Label(title_frame, text=IconManager.get_icon('transfer'), font=('Segoe UI', 24))
        app_icon.pack(side=tk.LEFT, padx=(0, ModernTheme.SPACING['sm']))
        
        title_label = ttk.Label(title_frame, text="File Transfer Pro", style='Heading.TLabel')
        title_label.pack(side=tk.LEFT)
        
        subtitle_label = ttk.Label(title_frame, text="Secure • Fast • Easy", style='Muted.TLabel')
        subtitle_label.pack(side=tk.LEFT, padx=(ModernTheme.SPACING['sm'], 0))
        
        # Quick actions
        actions_frame = ttk.Frame(header_frame)
        actions_frame.grid(row=0, column=1, sticky=tk.E)
        
        # Connection manager button
        self.connect_btn = ttk.Button(
            actions_frame,
            text=f"{IconManager.get_icon('network')} Connect",
            command=self._show_connection_manager,
            style='Primary.TButton'
        )
        self.connect_btn.pack(side=tk.RIGHT, padx=(ModernTheme.SPACING['sm'], 0))
        
        # Settings button
        settings_btn = ttk.Button(
            actions_frame,
            text=f"{IconManager.get_icon('settings')}",
            command=self._show_settings,
            style='Secondary.TButton'
        )
        settings_btn.pack(side=tk.RIGHT)
    
    def _create_main_content(self, parent):
        """Create the main content area."""
        # Create horizontal layout
        content_frame = ttk.Frame(parent)
        content_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        content_frame.columnconfigure(1, weight=1)
        content_frame.rowconfigure(0, weight=1)
        
        # Left sidebar
        self._create_sidebar(content_frame)
        
        # Main content area
        self._create_content_area(content_frame)
    
    def _create_sidebar(self, parent):
        """Create the left sidebar."""
        sidebar_frame = ttk.Frame(parent, style='Sidebar.TFrame', padding=ModernTheme.SPACING['md'])
        sidebar_frame.grid(row=0, column=0, sticky=(tk.W, tk.N, tk.S), padx=(0, ModernTheme.SPACING['md']))
        
        # Mode selection
        mode_label = ttk.Label(sidebar_frame, text="Mode", style='Subheading.TLabel')
        mode_label.pack(anchor=tk.W, pady=(0, ModernTheme.SPACING['sm']))
        
        self.mode_var = tk.StringVar(value="send")
        
        # Send mode
        send_radio = ttk.Radiobutton(
            sidebar_frame,
            text=f"{IconManager.get_icon('upload')} Send Files",
            variable=self.mode_var,
            value="send",
            command=self._on_mode_change
        )
        send_radio.pack(anchor=tk.W, pady=(0, ModernTheme.SPACING['xs']))
        
        # Receive mode
        receive_radio = ttk.Radiobutton(
            sidebar_frame,
            text=f"{IconManager.get_icon('download')} Receive Files",
            variable=self.mode_var,
            value="receive",
            command=self._on_mode_change
        )
        receive_radio.pack(anchor=tk.W, pady=(0, ModernTheme.SPACING['lg']))
        
        # Connection status
        status_label = ttk.Label(sidebar_frame, text="Connection", style='Subheading.TLabel')
        status_label.pack(anchor=tk.W, pady=(0, ModernTheme.SPACING['sm']))
        
        self.connection_status_frame = ttk.Frame(sidebar_frame, style='Card.TFrame', padding=ModernTheme.SPACING['sm'])
        self.connection_status_frame.pack(fill=tk.X, pady=(0, ModernTheme.SPACING['lg']))
        
        self.connection_icon = ttk.Label(self.connection_status_frame, text=IconManager.get_status_icon('disconnected'))
        self.connection_icon.pack(side=tk.LEFT)
        
        self.connection_text = ttk.Label(self.connection_status_frame, text="Not connected", style='Muted.TLabel')
        self.connection_text.pack(side=tk.LEFT, padx=(ModernTheme.SPACING['sm'], 0))
        
        # Quick stats
        stats_label = ttk.Label(sidebar_frame, text="Statistics", style='Subheading.TLabel')
        stats_label.pack(anchor=tk.W, pady=(0, ModernTheme.SPACING['sm']))
        
        self.stats_frame = ttk.Frame(sidebar_frame, style='Card.TFrame', padding=ModernTheme.SPACING['sm'])
        self.stats_frame.pack(fill=tk.X)
        
        self.files_count_label = ttk.Label(self.stats_frame, text="Files: 0", style='Muted.TLabel')
        self.files_count_label.pack(anchor=tk.W)
        
        self.total_size_label = ttk.Label(self.stats_frame, text="Size: 0 B", style='Muted.TLabel')
        self.total_size_label.pack(anchor=tk.W)
        
        self.transfer_speed_label = ttk.Label(self.stats_frame, text="Speed: --", style='Muted.TLabel')
        self.transfer_speed_label.pack(anchor=tk.W)
    
    def _create_content_area(self, parent):
        """Create the main content area."""
        self.content_frame = ttk.Frame(parent)
        self.content_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.content_frame.columnconfigure(0, weight=1)
        self.content_frame.rowconfigure(0, weight=1)
        
        # Create send and receive panels
        self._create_send_panel()
        self._create_receive_panel()
        
        # Show send panel by default
        self._on_mode_change()
    
    def _create_send_panel(self):
        """Create the send files panel."""
        self.send_panel = ttk.Frame(self.content_frame)
        
        # File selection area
        file_frame = ttk.LabelFrame(self.send_panel, text=f"{IconManager.get_icon('file')} File Selection", padding=ModernTheme.SPACING['md'])
        file_frame.pack(fill=tk.BOTH, expand=True, pady=(0, ModernTheme.SPACING['md']))
        file_frame.columnconfigure(0, weight=1)
        file_frame.rowconfigure(1, weight=1)
        
        # File selection buttons
        btn_frame = ttk.Frame(file_frame)
        btn_frame.pack(fill=tk.X, pady=(0, ModernTheme.SPACING['md']))
        
        ttk.Button(
            btn_frame,
            text=f"{IconManager.get_icon('add')} Add Files",
            command=self._add_files,
            style='Primary.TButton'
        ).pack(side=tk.LEFT, padx=(0, ModernTheme.SPACING['sm']))
        
        ttk.Button(
            btn_frame,
            text=f"{IconManager.get_icon('folder')} Add Folder",
            command=self._add_folder,
            style='Secondary.TButton'
        ).pack(side=tk.LEFT, padx=(0, ModernTheme.SPACING['sm']))
        
        ttk.Button(
            btn_frame,
            text=f"{IconManager.get_icon('clear')} Clear",
            command=self._clear_files,
            style='Secondary.TButton'
        ).pack(side=tk.LEFT)
        
        # File list with modern styling
        list_frame = ttk.Frame(file_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        self.file_tree = ttk.Treeview(
            list_frame,
            columns=('size', 'status', 'progress'),
            show='tree headings',
            style='Modern.Treeview'
        )
        
        self.file_tree.heading('#0', text='File Name')
        self.file_tree.heading('size', text='Size')
        self.file_tree.heading('status', text='Status')
        self.file_tree.heading('progress', text='Progress')
        
        self.file_tree.column('#0', width=300)
        self.file_tree.column('size', width=100)
        self.file_tree.column('status', width=100)
        self.file_tree.column('progress', width=100)
        
        self.file_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Scrollbar
        file_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_tree.yview)
        file_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.file_tree.configure(yscrollcommand=file_scrollbar.set)
        
        # Transfer controls
        transfer_frame = ttk.Frame(self.send_panel)
        transfer_frame.pack(fill=tk.X)
        transfer_frame.columnconfigure(1, weight=1)
        
        self.send_btn = ttk.Button(
            transfer_frame,
            text=f"{IconManager.get_icon('upload')} Send Files",
            command=self._send_files,
            style='Success.TButton',
            state='disabled'
        )
        self.send_btn.grid(row=0, column=0, padx=(0, ModernTheme.SPACING['md']))
        
        # Progress bar
        self.send_progress = ttk.Progressbar(
            transfer_frame,
            mode='determinate',
            style='Modern.Horizontal.TProgressbar'
        )
        self.send_progress.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, ModernTheme.SPACING['md']))
        
        # Progress label
        self.send_progress_label = ttk.Label(transfer_frame, text="Ready", style='Muted.TLabel')
        self.send_progress_label.grid(row=0, column=2)

    def _create_receive_panel(self):
        """Create the receive files panel."""
        self.receive_panel = ttk.Frame(self.content_frame)

        # Server configuration
        config_frame = ttk.LabelFrame(self.receive_panel, text=f"{IconManager.get_icon('server')} Server Configuration", padding=ModernTheme.SPACING['md'])
        config_frame.pack(fill=tk.X, pady=(0, ModernTheme.SPACING['md']))
        config_frame.columnconfigure(1, weight=1)

        # Listen address
        ttk.Label(config_frame, text="Listen Address:").grid(row=0, column=0, sticky=tk.W, padx=(0, ModernTheme.SPACING['sm']))
        self.listen_host_var = tk.StringVar(value="0.0.0.0")
        host_combo = ttk.Combobox(config_frame, textvariable=self.listen_host_var, state='readonly', style='Modern.TCombobox')
        host_combo['values'] = ['0.0.0.0 (All interfaces)', 'localhost (Local only)'] + NetworkUtils.get_all_local_ips()
        host_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, ModernTheme.SPACING['md']))

        # Port
        ttk.Label(config_frame, text="Port:").grid(row=0, column=2, sticky=tk.W, padx=(ModernTheme.SPACING['md'], ModernTheme.SPACING['sm']))
        self.listen_port_var = tk.StringVar(value="8888")
        port_entry = ttk.Entry(config_frame, textvariable=self.listen_port_var, width=10, style='Modern.TEntry')
        port_entry.grid(row=0, column=3, sticky=tk.W)

        # Download directory
        ttk.Label(config_frame, text="Download Directory:").grid(row=1, column=0, sticky=tk.W, padx=(0, ModernTheme.SPACING['sm']), pady=(ModernTheme.SPACING['md'], 0))

        dir_frame = ttk.Frame(config_frame)
        dir_frame.grid(row=1, column=1, columnspan=3, sticky=(tk.W, tk.E), pady=(ModernTheme.SPACING['md'], 0))
        dir_frame.columnconfigure(0, weight=1)

        self.download_dir_var = tk.StringVar(value=str(Path.home() / "Downloads" / "FileTransfer"))
        dir_entry = ttk.Entry(dir_frame, textvariable=self.download_dir_var, style='Modern.TEntry')
        dir_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, ModernTheme.SPACING['sm']))

        ttk.Button(
            dir_frame,
            text=f"{IconManager.get_icon('folder')} Browse",
            command=self._browse_download_dir,
            style='Secondary.TButton'
        ).grid(row=0, column=1)

        # Server controls
        control_frame = ttk.Frame(self.receive_panel)
        control_frame.pack(fill=tk.X, pady=(0, ModernTheme.SPACING['md']))

        self.start_server_btn = ttk.Button(
            control_frame,
            text=f"{IconManager.get_icon('play')} Start Server",
            command=self._start_server,
            style='Success.TButton'
        )
        self.start_server_btn.pack(side=tk.LEFT, padx=(0, ModernTheme.SPACING['sm']))

        self.stop_server_btn = ttk.Button(
            control_frame,
            text=f"{IconManager.get_icon('stop')} Stop Server",
            command=self._stop_server,
            style='Danger.TButton',
            state='disabled'
        )
        self.stop_server_btn.pack(side=tk.LEFT, padx=(0, ModernTheme.SPACING['md']))

        # Server status
        self.server_status = ttk.Label(control_frame, text="Server stopped", style='Muted.TLabel')
        self.server_status.pack(side=tk.LEFT)

        # Activity log
        log_frame = ttk.LabelFrame(self.receive_panel, text=f"{IconManager.get_icon('info')} Activity Log", padding=ModernTheme.SPACING['md'])
        log_frame.pack(fill=tk.BOTH, expand=True)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        # Create text widget with modern styling
        self.log_text = tk.Text(
            log_frame,
            wrap=tk.WORD,
            state='disabled',
            bg=ModernTheme.COLORS['bg_primary'],
            fg=ModernTheme.COLORS['text_primary'],
            font=ModernTheme.FONTS['monospace'],
            borderwidth=1,
            relief='solid'
        )
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Scrollbar
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

    def _create_status_bar(self, parent):
        """Create the modern status bar."""
        status_frame = ttk.Frame(parent, style='Card.TFrame', padding=ModernTheme.SPACING['sm'])
        status_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(ModernTheme.SPACING['lg'], 0))
        status_frame.columnconfigure(1, weight=1)

        # Status icon and text
        status_left = ttk.Frame(status_frame)
        status_left.grid(row=0, column=0, sticky=tk.W)

        self.status_icon = ttk.Label(status_left, text=IconManager.get_status_icon('ready'))
        self.status_icon.pack(side=tk.LEFT)

        self.status_label = ttk.Label(status_left, text="Ready", style='Muted.TLabel')
        self.status_label.pack(side=tk.LEFT, padx=(ModernTheme.SPACING['sm'], 0))

        # Network info
        status_right = ttk.Frame(status_frame)
        status_right.grid(row=0, column=1, sticky=tk.E)

        local_ip = NetworkUtils.get_local_ip()
        self.network_label = ttk.Label(status_right, text=f"Local IP: {local_ip}", style='Muted.TLabel')
        self.network_label.pack(side=tk.RIGHT)

    def _on_mode_change(self):
        """Handle mode change between send and receive."""
        mode = self.mode_var.get()

        # Hide all panels
        self.send_panel.grid_forget()
        self.receive_panel.grid_forget()

        # Show selected panel
        if mode == "send":
            self.send_panel.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
            self._update_send_button_state()
        else:
            self.receive_panel.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

    def _show_connection_manager(self):
        """Show the connection manager dialog."""
        dialog = ConnectionDialog(self.root, self.connection_manager)
        result = dialog.show()

        if result:
            # Switch to send mode and attempt connection
            self.mode_var.set("send")
            self._on_mode_change()
            self._connect_to_server(result['host'], result['port'])

    def _show_settings(self):
        """Show the settings dialog."""
        # TODO: Implement settings dialog
        messagebox.showinfo("Settings", "Settings dialog will be implemented in the next update.")

    def _connect_to_server(self, host: str, port: int):
        """Connect to a file transfer server."""
        if self.client and self.client.is_connected():
            self.client.disconnect()
            self._update_connection_status("disconnected", "Not connected")
            self.connect_btn.configure(text=f"{IconManager.get_icon('network')} Connect")
            return

        try:
            # Create enhanced client and setup callbacks
            self.client = EnhancedFileTransferClient(chunk_size=8192, max_retries=3)
            self._setup_client_callbacks()

            # Update UI
            self._update_connection_status("connecting", f"Connecting to {host}:{port}...")
            self.root.update()

            # Connect in background thread
            def connect_thread():
                try:
                    if self.client.connect(host, port):
                        self.root.after(0, lambda: self._on_connection_success(host, port))
                        # Add to history
                        self.connection_manager.add_to_history(host, port, True, 'outgoing')
                    else:
                        self.root.after(0, lambda: self._on_connection_failed(host, port))
                        # Add to history
                        self.connection_manager.add_to_history(host, port, False, 'outgoing')
                except Exception as e:
                    self.root.after(0, lambda: self._on_connection_error(str(e)))
                    # Add to history
                    self.connection_manager.add_to_history(host, port, False, 'outgoing')

            threading.Thread(target=connect_thread, daemon=True).start()

        except Exception as e:
            self._update_connection_status("disconnected", "Connection failed")
            messagebox.showerror("Connection Error", f"Error: {e}")

    def _on_connection_success(self, host: str, port: int):
        """Handle successful connection."""
        self._update_connection_status("connected", f"Connected to {host}:{port}")
        self.connect_btn.configure(text=f"{IconManager.get_icon('disconnect')} Disconnect")
        self._update_send_button_state()
        self._log_message(f"Connected to server {host}:{port}")

    def _on_connection_failed(self, host: str, port: int):
        """Handle connection failure."""
        self._update_connection_status("disconnected", "Connection failed")
        messagebox.showerror("Connection Error", f"Failed to connect to {host}:{port}")

    def _on_connection_error(self, error: str):
        """Handle connection error."""
        self._update_connection_status("disconnected", "Connection error")
        messagebox.showerror("Connection Error", f"Error: {error}")

    def _update_connection_status(self, status: str, text: str):
        """Update connection status display."""
        self.connection_icon.configure(text=IconManager.get_status_icon(status))
        self.connection_text.configure(text=text)

    def _update_send_button_state(self):
        """Update send button state based on connection and files."""
        if self.client and self.client.is_connected() and self.selected_files and not self.transfer_in_progress:
            self.send_btn.configure(state='normal')
        else:
            self.send_btn.configure(state='disabled')

    def _update_file_stats(self):
        """Update file statistics display."""
        file_count = len(self.selected_files)
        total_size = sum(FileUtils.get_file_size(f) for f in self.selected_files)

        self.files_count_label.configure(text=f"Files: {file_count}")
        self.total_size_label.configure(text=f"Size: {FileUtils.format_file_size(total_size)}")

    # File management methods
    def _add_files(self):
        """Add files to the transfer list."""
        files = filedialog.askopenfilenames(
            title="Select files to transfer",
            filetypes=[("All files", "*.*")]
        )

        for file_path in files:
            if file_path not in self.selected_files:
                self.selected_files.append(file_path)
                self._add_file_to_tree(file_path)

        self._update_file_stats()
        self._update_send_button_state()

    def _add_folder(self):
        """Add all files from a folder to the transfer list."""
        folder_path = filedialog.askdirectory(title="Select folder to transfer")

        if folder_path:
            folder = Path(folder_path)
            added_count = 0

            for file_path in folder.rglob('*'):
                if file_path.is_file():
                    file_str = str(file_path)
                    if file_str not in self.selected_files:
                        self.selected_files.append(file_str)
                        self._add_file_to_tree(file_str)
                        added_count += 1

            if added_count > 0:
                self._update_file_stats()
                self._update_send_button_state()
                self._log_message(f"Added {added_count} files from {folder_path}")

    def _clear_files(self):
        """Clear the file list."""
        self.selected_files.clear()
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)

        self._update_file_stats()
        self._update_send_button_state()

    def _add_file_to_tree(self, file_path: str):
        """Add a file to the tree view."""
        file_info = FileUtils.get_file_info(file_path)
        if file_info:
            item_id = self.file_tree.insert(
                '',
                'end',
                text=file_info['name'],
                values=(file_info['size_human'], 'Ready', '0%')
            )
            # Store file path in item for later reference
            self.file_tree.set(item_id, 'file_path', file_path)

    def _browse_download_dir(self):
        """Browse for download directory."""
        directory = filedialog.askdirectory(
            title="Select download directory",
            initialdir=self.download_dir_var.get()
        )

        if directory:
            self.download_dir_var.set(directory)

    # Server management methods
    def _start_server(self):
        """Start the file transfer server."""
        try:
            host = self.listen_host_var.get().split()[0]  # Extract IP from combo text
            port = int(self.listen_port_var.get().strip())
            download_dir = self.download_dir_var.get().strip()

            if not NetworkUtils.is_valid_port(port):
                messagebox.showerror("Error", "Invalid port number (1-65535)")
                return

            # Check if port is available
            if not NetworkUtils.is_port_available(host if host != "0.0.0.0" else "localhost", port):
                messagebox.showerror("Error", f"Port {port} is already in use")
                return

            # Create download directory
            if not FileUtils.ensure_directory_exists(download_dir):
                messagebox.showerror("Error", f"Cannot create download directory: {download_dir}")
                return

            # Create server and setup callbacks
            self.server = FileTransferServer(
                host=host,
                port=port,
                download_dir=download_dir,
                chunk_size=8192
            )
            self._setup_server_callbacks()

            # Start server in separate thread
            self.server_thread = threading.Thread(target=self.server.start, daemon=True)
            self.server_thread.start()

            # Update UI
            self.start_server_btn.configure(state='disabled')
            self.stop_server_btn.configure(state='normal')
            self.server_status.configure(text=f"Server running on {host}:{port}", style='Success.TLabel')
            self._log_message(f"Server started on {host}:{port}")
            self._log_message(f"Download directory: {download_dir}")

            # Update status
            self._update_status(f"Server running on {host}:{port}")

        except ValueError:
            messagebox.showerror("Error", "Invalid port number")
        except Exception as e:
            messagebox.showerror("Server Error", f"Error starting server: {e}")

    def _stop_server(self):
        """Stop the file transfer server."""
        if self.server:
            self.server.stop()
            self.server = None
            self.server_thread = None

            # Update UI
            self.start_server_btn.configure(state='normal')
            self.stop_server_btn.configure(state='disabled')
            self.server_status.configure(text="Server stopped", style='Muted.TLabel')
            self._log_message("Server stopped")
            self._update_status("Ready")

    def _send_files(self):
        """Send selected files to the server."""
        if not self.client or not self.client.is_connected():
            messagebox.showerror("Error", "Not connected to server")
            return

        if not self.selected_files:
            messagebox.showerror("Error", "No files selected")
            return

        # Start transfer
        self.transfer_in_progress = True
        self._update_send_button_state()

        # Create and show progress dialog
        progress_dialog = ProgressDialog(self.root, "Sending Files")

        def on_cancel():
            self.transfer_in_progress = False
            self._update_send_button_state()

        progress_dialog.on_cancel = on_cancel

        # Start transfer in separate thread
        def transfer_thread():
            try:
                total_files = len(self.selected_files)
                total_bytes = sum(FileUtils.get_file_size(f) for f in self.selected_files)
                transferred_bytes = 0

                for i, file_path in enumerate(self.selected_files):
                    if progress_dialog.is_cancelled:
                        break

                    file_name = Path(file_path).name
                    file_size = FileUtils.get_file_size(file_path)

                    # Update progress dialog
                    overall_progress = (i / total_files) * 100
                    progress_dialog.update_progress(
                        file_name, 0, overall_progress,
                        transferred_bytes, total_bytes, i, total_files
                    )

                    # Send file
                    success = self.client.send_file(file_path)

                    if success:
                        transferred_bytes += file_size
                        # Update file status in tree
                        self._update_file_status(file_name, "Sent", "100%")
                    else:
                        self._update_file_status(file_name, "Failed", "0%")

                    # Final progress update for this file
                    overall_progress = ((i + 1) / total_files) * 100
                    progress_dialog.update_progress(
                        file_name, 100, overall_progress,
                        transferred_bytes, total_bytes, i + 1, total_files
                    )

                # Mark as completed
                progress_dialog.set_completed(not progress_dialog.is_cancelled)

            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("Transfer Error", f"Error during transfer: {e}"))
                progress_dialog.set_completed(False)
            finally:
                self.root.after(0, lambda: setattr(self, 'transfer_in_progress', False))
                self.root.after(0, self._update_send_button_state)

        threading.Thread(target=transfer_thread, daemon=True).start()
        progress_dialog.show()

    def _update_file_status(self, filename: str, status: str, progress: str):
        """Update file status in the tree view."""
        for item in self.file_tree.get_children():
            if self.file_tree.item(item, 'text') == filename:
                current_values = list(self.file_tree.item(item, 'values'))
                current_values[1] = status  # Status column
                current_values[2] = progress  # Progress column
                self.file_tree.item(item, values=current_values)
                break

    # Callback setup methods
    def _setup_server_callbacks(self):
        """Setup callbacks for server events."""
        if self.server:
            self.server.on_client_connected = self._on_client_connected
            self.server.on_client_disconnected = self._on_client_disconnected
            self.server.on_file_received = self._on_file_received
            self.server.on_transfer_progress = self._on_server_transfer_progress

    def _setup_client_callbacks(self):
        """Setup callbacks for client events."""
        if self.client:
            self.client.on_connected = self._on_connected
            self.client.on_disconnected = self._on_disconnected
            self.client.on_transfer_progress = self._on_client_transfer_progress
            self.client.on_transfer_complete = self._on_transfer_complete
            self.client.on_transfer_error = self._on_transfer_error

    # Server event callbacks
    def _on_client_connected(self, client_id: str, client_address: tuple):
        """Handle client connection."""
        message = f"Client connected: {client_id}"
        self.root.after(0, lambda: self._log_message(message))
        # Add to history
        host, port = client_address
        self.connection_manager.add_to_history(host, port, True, 'incoming')

    def _on_client_disconnected(self, client_id: str):
        """Handle client disconnection."""
        message = f"Client disconnected: {client_id}"
        self.root.after(0, lambda: self._log_message(message))

    def _on_file_received(self, filename: str, client_id: str):
        """Handle file received."""
        message = f"File received: {filename} from {client_id}"
        self.root.after(0, lambda: self._log_message(message))

    def _on_server_transfer_progress(self, filename: str, progress: float, bytes_received: int, total_bytes: int, client_id: str):
        """Handle server transfer progress."""
        speed_text = f"{FileUtils.format_file_size(bytes_received)}/{FileUtils.format_file_size(total_bytes)}"
        self.root.after(0, lambda: self.transfer_speed_label.configure(text=f"Speed: {speed_text}"))

    # Client event callbacks
    def _on_connected(self, host: str, port: int):
        """Handle successful connection."""
        pass  # Already handled in _on_connection_success

    def _on_disconnected(self):
        """Handle disconnection."""
        self.root.after(0, lambda: self._update_connection_status("disconnected", "Disconnected"))
        self.root.after(0, lambda: self.connect_btn.configure(text=f"{IconManager.get_icon('network')} Connect"))
        self.root.after(0, self._update_send_button_state)

    def _on_client_transfer_progress(self, filename: str, progress: float, bytes_sent: int, total_bytes: int):
        """Handle client transfer progress."""
        self.root.after(0, lambda: self.send_progress.configure(value=progress))
        speed_text = f"{FileUtils.format_file_size(bytes_sent)}/{FileUtils.format_file_size(total_bytes)}"
        self.root.after(0, lambda: self.send_progress_label.configure(text=f"Sending: {progress:.1f}%"))
        self.root.after(0, lambda: self.transfer_speed_label.configure(text=f"Speed: {speed_text}"))

    def _on_transfer_complete(self, filename: str, success: bool):
        """Handle transfer completion."""
        if success:
            message = f"Transfer completed: {filename}"
        else:
            message = f"Transfer failed: {filename}"
        self.root.after(0, lambda: self._log_message(message))

    def _on_transfer_error(self, filename: str, error: str):
        """Handle transfer error."""
        message = f"Transfer error for {filename}: {error}"
        self.root.after(0, lambda: self._log_message(message))

    # Utility methods
    def _log_message(self, message: str):
        """Add a message to the activity log."""
        self.log_text.configure(state='normal')

        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)
        self.log_text.configure(state='disabled')

    def _update_status(self, message: str):
        """Update the status bar."""
        self.status_label.configure(text=message)
        if "error" in message.lower() or "failed" in message.lower():
            self.status_icon.configure(text=IconManager.get_status_icon('error'))
        elif "connected" in message.lower() or "running" in message.lower():
            self.status_icon.configure(text=IconManager.get_status_icon('connected'))
        else:
            self.status_icon.configure(text=IconManager.get_status_icon('ready'))

    def _center_window(self):
        """Center the window on the screen."""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def _on_closing(self):
        """Handle window closing event."""
        if self.server:
            self._stop_server()
        if self.client and self.client.is_connected():
            self.client.disconnect()
        self.root.destroy()

    def run(self):
        """Start the GUI application."""
        self.logger.info("Starting modern GUI application")
        self._log_message("File Transfer Pro started")
        self.root.mainloop()
